name: Flutter发布版APK构建

on:
  # 手动触发工作流
  workflow_dispatch:
    inputs:
      version_name:
        description: '版本名称 (例如: 1.0.0)'
        required: true
        default: '1.0.0'

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: 检出代码
        uses: actions/checkout@v3

      - name: 设置 Java 环境
        uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: '17'

      - name: 设置 Flutter 环境
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.29.2'
          channel: 'stable'
          cache: true
      
      - name: 准备构建环境
        run: |
          # 设置Java环境变量用于大内存构建
          echo "GRADLE_OPTS=-Xmx2g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+UnlockExperimentalVMOptions --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED" >> $GITHUB_ENV

      - name: 创建签名密钥
        run: |
          # 移除可能的额外字符并解码
          echo "${{ secrets.KEYSTORE_BASE64 }}" | tr -d '\r' | sed 's/-----BEGIN CERTIFICATE-----//g' | sed 's/-----END CERTIFICATE-----//g' | base64 --decode > android/app/upload-keystore.jks
          
          # 验证JKS文件是否正确生成
          if [ -s android/app/upload-keystore.jks ]; then
            echo "密钥库文件成功创建"
            ls -la android/app/upload-keystore.jks
          else
            echo "错误：密钥库文件为空或未创建，创建调试密钥库"
            keytool -genkeypair -v -keystore android/app/upload-keystore.jks -storepass android -alias androiddebugkey -keypass android -keyalg RSA -keysize 2048 -validity 10000 -dname "CN=Android Debug,O=Android,C=US"
          fi
          
          # 创建key.properties文件
          cat > android/key.properties << EOF
          storePassword=${{ secrets.KEYSTORE_PASSWORD || 'android' }}
          keyPassword=${{ secrets.KEY_PASSWORD || 'android' }}
          keyAlias=${{ secrets.KEY_ALIAS || 'androiddebugkey' }}
          storeFile=upload-keystore.jks
          EOF
          
          # 显示key.properties内容（不显示密码）
          echo "key.properties文件已创建"

      - name: 安装Flutter依赖
        run: |
          flutter --version
          flutter doctor -v
          flutter pub get
          flutter pub upgrade

      - name: 构建发布版APK
        run: |
          flutter clean
          
          # 显示gradle.properties内容
          echo "gradle.properties内容:"
          cat android/gradle.properties
          
          # 构建APK，明确指定32位和64位架构
          flutter build apk --release \
            --target-platform=android-arm,android-arm64 \
            --build-number=${{ github.run_number }} \
            --build-name=${{ github.event.inputs.version_name }} \
            --no-shrink \
            --no-obfuscate \
            --android-skip-build-dependency-validation

      - name: 上传APK
        uses: actions/upload-artifact@v4
        with:
          name: app-release-signed
          path: build/app/outputs/flutter-apk/app-release.apk
          retention-days: 30