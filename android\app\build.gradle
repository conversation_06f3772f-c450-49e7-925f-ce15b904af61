plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

// 加载签名配置
def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}


def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

android {
    namespace "com.shangjin.thoughtecho"
    compileSdk 35
    ndkVersion "27.0.12077973"

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        applicationId "com.shangjin.thoughtecho"
        minSdkVersion 23
        targetSdkVersion 35
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        multiDexEnabled true

        // ARM32 binder64设备优化配置
        manifestPlaceholders = [
            'applicationName': 'io.flutter.app.FlutterApplication',
            'largeHeap': 'true',
            'hardwareAccelerated': 'false'
        ]



        // NDK配置 - 恢复原始配置以避免兼容性问题
        ndk {
            abiFilters 'armeabi-v7a', 'arm64-v8a', 'x86', 'x86_64'
        }

        // 内存和性能优化
        vectorDrawables.useSupportLibrary = true
        resConfigs "en", "zh"
    }

    signingConfigs {
        release {
            if (keystoreProperties.containsKey('storeFile')) {
                keyAlias keystoreProperties['keyAlias']
                keyPassword keystoreProperties['keyPassword']
                storeFile file(keystoreProperties['storeFile'])
                storePassword keystoreProperties['storePassword']
            } else {
                // 如果没有找到签名配置，使用debug签名
                println "警告: 未找到签名配置，将使用debug签名"
                keyAlias 'androiddebugkey'
                keyPassword 'android'
                storeFile file('debug.keystore')
                storePassword 'android'
            }
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled true   // 启用代码混淆以减少APK大小
            shrinkResources true // 启用资源压缩
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'

            // 大文件处理优化
            manifestPlaceholders = [
                'enableCrashlytics': 'true'
            ]

            // 性能优化
            zipAlignEnabled true
            debuggable false
        }
        debug {
            signingConfig signingConfigs.debug
            minifyEnabled false
            shrinkResources false
            debuggable true

            // 调试模式的内存优化
            manifestPlaceholders = [
                'enableCrashlytics': 'false'
            ]
        }
    }

    // 打包优化
    packagingOptions {
        pickFirst '**/libc++_shared.so'
        pickFirst '**/libjsc.so'
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/NOTICE.txt'
    }
}

flutter {
    source '../..'
}

dependencies {
    implementation 'androidx.multidex:multidex:2.0.1'
    implementation 'com.tencent:mmkv:1.3.0'
}