<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>心迹 {{YEAR}} 年度报告</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
            line-height: 1.6;
        }
        .container {
            max-width: 414px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 50px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 50px 20px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        .year {
            font-size: 52px;
            font-weight: 800;
            margin-bottom: 10px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
            background: linear-gradient(45deg, #fff, #f0f8ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .subtitle {
            font-size: 20px;
            opacity: 0.95;
            margin-bottom: 30px;
            font-weight: 300;
            letter-spacing: 1px;
        }
        .stats-overview {
            display: flex;
            justify-content: space-around;
            margin-top: 30px;
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            padding: 20px 10px;
            backdrop-filter: blur(10px);
        }
        .stat-item { text-align: center; flex: 1; }
        .stat-number {
            font-size: 28px;
            font-weight: 700;
            display: block;
            margin-bottom: 5px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        .stat-label {
            font-size: 13px;
            opacity: 0.9;
            font-weight: 400;
            letter-spacing: 0.5px;
        }
        .section {
            padding: 35px 25px;
            border-bottom: 1px solid #f5f5f5;
            position: relative;
        }
        .section:last-child { border-bottom: none; }
        .section-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 25px;
            color: #2c3e50;
            display: flex;
            align-items: center;
            position: relative;
        }
        .section-title::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 40px;
            width: 40px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }
        .section-icon {
            margin-right: 12px;
            font-size: 26px;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
        }
        .highlight-card {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            border-radius: 20px;
            padding: 25px;
            margin: 20px 0;
            color: white;
            text-align: center;
            box-shadow: 0 12px 40px rgba(255, 154, 158, 0.3);
            position: relative;
            overflow: hidden;
        }
        .highlight-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 70%);
        }
        .highlight-number {
            font-size: 42px;
            font-weight: 800;
            margin-bottom: 10px;
            text-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }
        .highlight-text {
            font-size: 17px;
            opacity: 0.95;
            font-weight: 400;
            line-height: 1.4;
        }
        .chart-container {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 16px;
            padding: 25px;
            margin: 20px 0;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
        }
        .chart-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #495057;
        }
        .month-chart {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 12px;
            margin: 20px 0;
        }
        .month-item {
            text-align: center;
            padding: 15px 8px;
            border-radius: 12px;
            background: white;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        .month-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
        }
        .month-name {
            font-size: 13px;
            color: #6c757d;
            margin-bottom: 8px;
            font-weight: 500;
        }
        .month-count {
            font-size: 20px;
            font-weight: 700;
            color: #667eea;
            text-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .tag-cloud {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            margin: 20px 0;
            justify-content: center;
        }
        .tag {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 10px 18px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        .tag:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }
        .tag.popular {
            font-size: 16px;
            padding: 12px 22px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
        }
        .insight-box {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 16px;
            padding: 25px;
            margin: 20px 0;
            border-left: 5px solid #ff6b6b;
            box-shadow: 0 8px 25px rgba(252, 182, 159, 0.3);
            position: relative;
        }
        .insight-box::before {
            content: '💡';
            position: absolute;
            top: 20px;
            right: 20px;
            font-size: 24px;
            opacity: 0.7;
        }
        .insight-title {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 12px;
            color: #2c3e50;
        }
        .insight-text {
            font-size: 15px;
            line-height: 1.7;
            color: #5a5a5a;
            font-weight: 400;
        }
        .quote-card {
            background: white;
            border-radius: 16px;
            padding: 25px;
            margin: 20px 0;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-left: 5px solid #667eea;
            position: relative;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        .quote-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(0,0,0,0.15);
        }
        .quote-card::before {
            content: '"';
            position: absolute;
            top: 15px;
            left: 15px;
            font-size: 40px;
            color: #667eea;
            opacity: 0.3;
            font-family: serif;
        }
        .quote-content {
            font-size: 16px;
            line-height: 1.7;
            margin-bottom: 15px;
            font-style: italic;
            color: #444;
            padding-left: 20px;
        }
        .quote-date {
            font-size: 13px;
            color: #999;
            text-align: right;
            font-weight: 500;
        }
        .achievement-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 18px;
            margin: 20px 0;
        }
        .achievement {
            background: white;
            border-radius: 16px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        .achievement:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(0,0,0,0.15);
        }
        .achievement-icon {
            font-size: 36px;
            margin-bottom: 12px;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
        }
        .achievement-title {
            font-size: 15px;
            font-weight: 700;
            margin-bottom: 8px;
            color: #2c3e50;
        }
        .achievement-desc {
            font-size: 13px;
            color: #6c757d;
            line-height: 1.4;
        }
        .footer {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 40px 25px;
            text-align: center;
            border-top: 1px solid #dee2e6;
        }
        .footer-text {
            font-size: 15px;
            color: #6c757d;
            margin-bottom: 12px;
            line-height: 1.5;
        }
        .footer-logo {
            font-size: 22px;
            font-weight: 800;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-top: 10px;
        }
        .growth-indicator {
            display: inline-flex;
            align-items: center;
            background: linear-gradient(135deg, #e8f5e8, #d4edda);
            color: #155724;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 13px;
            font-weight: 600;
            margin-left: 10px;
            box-shadow: 0 2px 8px rgba(21, 87, 36, 0.2);
        }
        .growth-indicator::before {
            content: '📈';
            margin-right: 6px;
        }
        .scroll-indicator {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            cursor: pointer;
            transition: transform 0.2s ease;
        }
        .scroll-indicator:hover {
            transform: scale(1.1);
        }
        @media (max-width: 414px) {
            .container { max-width: 100%; }
            .year { font-size: 48px; }
            .section { padding: 30px 20px; }
            .highlight-card { padding: 20px; }
            .chart-container { padding: 20px; }
        }
        @media (max-width: 375px) {
            .stats-overview { flex-direction: column; gap: 15px; }
            .stat-item { margin-bottom: 10px; }
            .achievement-grid { grid-template-columns: 1fr; }
            .month-chart { grid-template-columns: repeat(3, 1fr); }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <div class="year">{{YEAR}}</div>
            <div class="subtitle">✨ 我的思考轨迹 ✨</div>
            <div class="stats-overview">
                <div class="stat-item">
                    <span class="stat-number">{{ACTIVE_DAYS}}</span>
                    <div class="stat-label">📅 记录天数</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number">{{TOTAL_NOTES}}</span>
                    <div class="stat-label">📝 总笔记数</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number">{{TOTAL_TAGS}}</span>
                    <div class="stat-label">🏷️ 使用标签</div>
                </div>
            </div>
        </div>

        <!-- 总体数据 -->
        <div class="section">
            <div class="section-title">
                <span class="section-icon">📊</span>
                年度数据概览
            </div>
            
            <div class="highlight-card">
                <div class="highlight-number">{{TOTAL_NOTES}}</div>
                <div class="highlight-text">
                    今年共记录了 {{TOTAL_NOTES}} 条笔记
                    <span class="growth-indicator">{{GROWTH_PERCENTAGE}}</span>
                </div>
            </div>

            <div class="chart-container">
                <div class="chart-title">月度笔记数量</div>
                <div class="month-chart">
                    {{MONTHLY_CHART}}
                </div>
            </div>
        </div>

        <!-- 标签分析 -->
        <div class="section">
            <div class="section-title">
                <span class="section-icon">🏷️</span>
                标签使用分析
            </div>
            
            <div class="insight-box">
                <div class="insight-title">最活跃的思考领域</div>
                <div class="insight-text">
                    {{TAG_INSIGHT}}
                </div>
            </div>

            <div class="tag-cloud">
                {{TAG_CLOUD}}
            </div>
        </div>

        <!-- 写作习惯 -->
        <div class="section">
            <div class="section-title">
                <span class="section-icon">⏰</span>
                写作习惯分析
            </div>
            
            <div class="chart-container">
                <div class="chart-title">最活跃的记录时间</div>
                <div style="text-align: center; margin: 20px 0;">
                    <div style="font-size: 24px; font-weight: bold; color: #667eea; margin-bottom: 10px;">
                        {{PEAK_TIME}}
                    </div>
                    <div style="font-size: 14px; color: #666;">
                        {{PEAK_TIME_DESC}}
                    </div>
                </div>
            </div>

            <div class="insight-box">
                <div class="insight-title">写作偏好发现</div>
                <div class="insight-text">
                    {{WRITING_HABITS}}
                </div>
            </div>
        </div>

        <!-- 精彩回顾 -->
        <div class="section">
            <div class="section-title">
                <span class="section-icon">✨</span>
                年度精彩回顾
            </div>
            
            {{FEATURED_QUOTES}}
        </div>

        <!-- 成就解锁 -->
        <div class="section">
            <div class="section-title">
                <span class="section-icon">🏆</span>
                成就解锁
            </div>
            
            <div class="achievement-grid">
                {{ACHIEVEMENTS}}
            </div>
        </div>

        <!-- 未来展望 -->
        <div class="section">
            <div class="section-title">
                <span class="section-icon">🚀</span>
                {{NEXT_YEAR}} 年展望
            </div>
            
            <div class="insight-box">
                <div class="insight-title">基于你的记录习惯建议</div>
                <div class="insight-text">
                    {{FUTURE_SUGGESTIONS}}
                </div>
            </div>
        </div>

        <!-- 页脚 -->
        <div class="footer">
            <div class="footer-text">
                🙏 感谢你与心迹一起记录美好的 {{YEAR}} 年
            </div>
            <div class="footer-text">
                🚀 继续在 {{NEXT_YEAR}} 年捕捉每一个珍贵的想法 ✨
            </div>
            <div class="footer-logo">心迹 ThoughtEcho</div>
        </div>

        <!-- 回到顶部按钮 -->
        <div class="scroll-indicator" onclick="scrollToTop()">
            ⬆️
        </div>
    </div>

    <script>
        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // 添加滚动动画
        window.addEventListener('scroll', function() {
            const scrollIndicator = document.querySelector('.scroll-indicator');
            if (window.scrollY > 300) {
                scrollIndicator.style.opacity = '1';
            } else {
                scrollIndicator.style.opacity = '0';
            }
        });

        // 页面加载动画
        window.addEventListener('load', function() {
            document.body.style.opacity = '0';
            document.body.style.transition = 'opacity 0.5s ease-in-out';
            setTimeout(() => {
                document.body.style.opacity = '1';
            }, 100);
        });
    </script>
</body>
</html>
